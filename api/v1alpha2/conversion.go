// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package v1alpha2

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/conversion"

	v1alpha1 "github.com/envoyproxy/ai-gateway/api/v1alpha1"
)

// ConvertTo converts this AIRoute to the Hub version (v1alpha1).
func (src *AIRoute) ConvertTo(dstRaw conversion.Hub) error {
	dst := dstRaw.(*v1alpha1.AIGatewayRoute)

	// Convert metadata
	dst.ObjectMeta = src.ObjectMeta

	// Convert spec
	dst.Spec.TargetRefs = src.Spec.TargetRefs
	dst.Spec.APISchema = v1alpha1.VersionedAPISchema{
		Name:    v1alpha1.APISchema(src.Spec.APISchema.Name),
		Version: src.Spec.APISchema.Version,
	}

	// Convert rules
	dst.Spec.Rules = make([]v1alpha1.AIGatewayRouteRule, len(src.Spec.Rules))
	for i, rule := range src.Spec.Rules {
		dst.Spec.Rules[i] = v1alpha1.AIGatewayRouteRule{
			BackendRefs: make([]v1alpha1.AIGatewayRouteRuleBackendRef, len(rule.BackendRefs)),
			Matches:     make([]v1alpha1.AIGatewayRouteRuleMatch, len(rule.Matches)),
		}

		// Convert backend refs
		for j, backendRef := range rule.BackendRefs {
			var priority *uint32
			if backendRef.Priority != nil {
				p := uint32(*backendRef.Priority)
				priority = &p
			}
			dst.Spec.Rules[i].BackendRefs[j] = v1alpha1.AIGatewayRouteRuleBackendRef{
				Name:              backendRef.Name,
				ModelNameOverride: backendRef.ModelNameOverride,
				Weight:            backendRef.Weight,
				Priority:          priority,
			}
		}

		// Convert matches - v1alpha1 only supports Headers
		for j, match := range rule.Matches {
			dst.Spec.Rules[i].Matches[j] = v1alpha1.AIGatewayRouteRuleMatch{
				Headers: match.Headers,
			}
		}
	}

	// Convert LLMRequestCosts (directly in spec in v1alpha1)
	if src.Spec.FilterConfig != nil {
		dst.Spec.LLMRequestCosts = make([]v1alpha1.LLMRequestCost, len(src.Spec.FilterConfig.LLMRequestCosts))
		for i, cost := range src.Spec.FilterConfig.LLMRequestCosts {
			dst.Spec.LLMRequestCosts[i] = v1alpha1.LLMRequestCost{
				MetadataKey: cost.MetadataKey,
				Type:        v1alpha1.LLMRequestCostType(cost.Type),
				CEL:         cost.CEL,
			}
		}

		// Convert filter config
		if src.Spec.FilterConfig.ExtProcConfig != nil {
			dst.Spec.FilterConfig = &v1alpha1.AIGatewayFilterConfig{
				Type: v1alpha1.AIGatewayFilterConfigTypeExternalProcessor,
				ExternalProcessor: &v1alpha1.AIGatewayFilterConfigExternalProcessor{
					Resources: src.Spec.FilterConfig.ExtProcConfig.Resources,
				},
			}
		}
	}

	// Convert status
	dst.Status.Conditions = make([]metav1.Condition, len(src.Status.Conditions))
	for i, condition := range src.Status.Conditions {
		dst.Status.Conditions[i] = metav1.Condition{
			Type:               condition.Type,
			Status:             condition.Status,
			ObservedGeneration: condition.ObservedGeneration,
			LastTransitionTime: condition.LastTransitionTime,
			Reason:             condition.Reason,
			Message:            condition.Message,
		}
	}

	return nil
}

// ConvertFrom converts from the Hub version (v1alpha1) to this version.
func (dst *AIRoute) ConvertFrom(srcRaw conversion.Hub) error {
	src := srcRaw.(*v1alpha1.AIGatewayRoute)

	// Convert metadata
	dst.ObjectMeta = src.ObjectMeta

	// Convert spec
	dst.Spec.TargetRefs = src.Spec.TargetRefs
	dst.Spec.APISchema = VersionedAPISchema{
		Name:    APISchema(src.Spec.APISchema.Name),
		Version: src.Spec.APISchema.Version,
	}

	// Convert rules
	dst.Spec.Rules = make([]AIRouteRule, len(src.Spec.Rules))
	for i, rule := range src.Spec.Rules {
		dst.Spec.Rules[i] = AIRouteRule{
			BackendRefs: make([]AIBackendRef, len(rule.BackendRefs)),
			Matches:     make([]AIRouteMatch, len(rule.Matches)),
		}

		// Convert backend refs
		for j, backendRef := range rule.BackendRefs {
			var priority *int32
			if backendRef.Priority != nil {
				p := int32(*backendRef.Priority)
				priority = &p
			}
			dst.Spec.Rules[i].BackendRefs[j] = AIBackendRef{
				Name:              backendRef.Name,
				ModelNameOverride: backendRef.ModelNameOverride,
				Weight:            backendRef.Weight,
				Priority:          priority,
			}
		}

		// Convert matches - v1alpha1 only has Headers, so we only convert that
		for j, match := range rule.Matches {
			dst.Spec.Rules[i].Matches[j] = AIRouteMatch{
				Headers: match.Headers,
				// Path, QueryParams, Method are not available in v1alpha1, so we leave them nil
			}
		}
	}

	// Convert LLMRequestCosts (directly in spec in v1alpha1)
	if len(src.Spec.LLMRequestCosts) > 0 || src.Spec.FilterConfig != nil {
		dst.Spec.FilterConfig = &FilterConfig{
			LLMRequestCosts: make([]LLMRequestCost, len(src.Spec.LLMRequestCosts)),
		}

		for i, cost := range src.Spec.LLMRequestCosts {
			dst.Spec.FilterConfig.LLMRequestCosts[i] = LLMRequestCost{
				MetadataKey: cost.MetadataKey,
				Type:        LLMRequestCostType(cost.Type),
				CEL:         cost.CEL,
			}
		}

		// Convert filter config
		if src.Spec.FilterConfig != nil && src.Spec.FilterConfig.ExternalProcessor != nil {
			dst.Spec.FilterConfig.ExtProcConfig = &ExtProcConfig{
				Resources: src.Spec.FilterConfig.ExternalProcessor.Resources,
			}
		}
	}

	// Convert status
	dst.Status.Conditions = make([]metav1.Condition, len(src.Status.Conditions))
	for i, condition := range src.Status.Conditions {
		dst.Status.Conditions[i] = metav1.Condition{
			Type:               condition.Type,
			Status:             condition.Status,
			ObservedGeneration: condition.ObservedGeneration,
			LastTransitionTime: condition.LastTransitionTime,
			Reason:             condition.Reason,
			Message:            condition.Message,
		}
	}

	return nil
}

// ConvertTo converts this AIBackend to the Hub version (v1alpha1).
func (src *AIBackend) ConvertTo(dstRaw conversion.Hub) error {
	dst := dstRaw.(*v1alpha1.AIServiceBackend)

	// Convert metadata
	dst.ObjectMeta = src.ObjectMeta

	// Convert spec
	dst.Spec.APISchema = v1alpha1.VersionedAPISchema{
		Name:    v1alpha1.APISchema(src.Spec.APISchema.Name),
		Version: src.Spec.APISchema.Version,
	}
	dst.Spec.BackendRef = src.Spec.BackendRef
	dst.Spec.BackendSecurityPolicyRef = src.Spec.BackendSecurityPolicyRef

	// Convert status
	dst.Status.Conditions = make([]metav1.Condition, len(src.Status.Conditions))
	for i, condition := range src.Status.Conditions {
		dst.Status.Conditions[i] = metav1.Condition{
			Type:               condition.Type,
			Status:             condition.Status,
			ObservedGeneration: condition.ObservedGeneration,
			LastTransitionTime: condition.LastTransitionTime,
			Reason:             condition.Reason,
			Message:            condition.Message,
		}
	}

	return nil
}

// ConvertFrom converts from the Hub version (v1alpha1) to this version.
func (dst *AIBackend) ConvertFrom(srcRaw conversion.Hub) error {
	src := srcRaw.(*v1alpha1.AIServiceBackend)

	// Convert metadata
	dst.ObjectMeta = src.ObjectMeta

	// Convert spec
	dst.Spec.APISchema = VersionedAPISchema{
		Name:    APISchema(src.Spec.APISchema.Name),
		Version: src.Spec.APISchema.Version,
	}
	dst.Spec.BackendRef = src.Spec.BackendRef
	dst.Spec.BackendSecurityPolicyRef = src.Spec.BackendSecurityPolicyRef

	// Convert status
	dst.Status.Conditions = make([]metav1.Condition, len(src.Status.Conditions))
	for i, condition := range src.Status.Conditions {
		dst.Status.Conditions[i] = metav1.Condition{
			Type:               condition.Type,
			Status:             condition.Status,
			ObservedGeneration: condition.ObservedGeneration,
			LastTransitionTime: condition.LastTransitionTime,
			Reason:             condition.Reason,
			Message:            condition.Message,
		}
	}

	return nil
}

// ConvertTo converts this BackendSecurityPolicy to the Hub version (v1alpha1).
func (src *BackendSecurityPolicy) ConvertTo(dstRaw conversion.Hub) error {
	dst := dstRaw.(*v1alpha1.BackendSecurityPolicy)

	// Convert metadata
	dst.ObjectMeta = src.ObjectMeta

	// Convert spec
	dst.Spec.Type = v1alpha1.BackendSecurityPolicyType(src.Spec.Type)

	// For now, we'll do a simple conversion. Full field-by-field conversion can be added later.
	// Since the structures are identical between v1alpha1 and v1alpha2, we can use JSON marshaling
	// as a workaround for deep conversion.
	if src.Spec.APIKey != nil {
		dst.Spec.APIKey = &v1alpha1.BackendSecurityPolicyAPIKey{
			SecretRef: src.Spec.APIKey.SecretRef,
		}
	}
	// TODO: Add detailed conversion for AWSCredentials, AzureCredentials, GCPCredentials when needed

	// Convert status
	dst.Status.Conditions = make([]metav1.Condition, len(src.Status.Conditions))
	for i, condition := range src.Status.Conditions {
		dst.Status.Conditions[i] = metav1.Condition{
			Type:               condition.Type,
			Status:             condition.Status,
			ObservedGeneration: condition.ObservedGeneration,
			LastTransitionTime: condition.LastTransitionTime,
			Reason:             condition.Reason,
			Message:            condition.Message,
		}
	}

	return nil
}

// ConvertFrom converts from the Hub version (v1alpha1) to this version.
func (dst *BackendSecurityPolicy) ConvertFrom(srcRaw conversion.Hub) error {
	src := srcRaw.(*v1alpha1.BackendSecurityPolicy)

	// Convert metadata
	dst.ObjectMeta = src.ObjectMeta

	// Convert spec
	dst.Spec.Type = BackendSecurityPolicyType(src.Spec.Type)

	// For now, we'll do a simple conversion. Full field-by-field conversion can be added later.
	if src.Spec.APIKey != nil {
		dst.Spec.APIKey = &BackendSecurityPolicyAPIKey{
			SecretRef: src.Spec.APIKey.SecretRef,
		}
	}
	// TODO: Add detailed conversion for AWSCredentials, AzureCredentials, GCPCredentials when needed

	// Convert status
	dst.Status.Conditions = make([]metav1.Condition, len(src.Status.Conditions))
	for i, condition := range src.Status.Conditions {
		dst.Status.Conditions[i] = metav1.Condition{
			Type:               condition.Type,
			Status:             condition.Status,
			ObservedGeneration: condition.ObservedGeneration,
			LastTransitionTime: condition.LastTransitionTime,
			Reason:             condition.Reason,
			Message:            condition.Message,
		}
	}

	return nil
}
