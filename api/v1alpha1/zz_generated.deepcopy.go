//go:build !ignore_autogenerated

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha2

import (
	"k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	apisv1 "sigs.k8s.io/gateway-api/apis/v1"
	apisv1alpha2 "sigs.k8s.io/gateway-api/apis/v1alpha2"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIBackend) DeepCopyInto(out *AIBackend) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIBackend.
func (in *AIBackend) DeepCopy() *AIBackend {
	if in == nil {
		return nil
	}
	out := new(AIBackend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *AIBackend) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIBackendList) DeepCopyInto(out *AIBackendList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]AIBackend, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIBackendList.
func (in *AIBackendList) DeepCopy() *AIBackendList {
	if in == nil {
		return nil
	}
	out := new(AIBackendList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *AIBackendList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIBackendRef) DeepCopyInto(out *AIBackendRef) {
	*out = *in
	if in.Weight != nil {
		in, out := &in.Weight, &out.Weight
		*out = new(int32)
		**out = **in
	}
	if in.Priority != nil {
		in, out := &in.Priority, &out.Priority
		*out = new(int32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIBackendRef.
func (in *AIBackendRef) DeepCopy() *AIBackendRef {
	if in == nil {
		return nil
	}
	out := new(AIBackendRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIBackendSpec) DeepCopyInto(out *AIBackendSpec) {
	*out = *in
	in.APISchema.DeepCopyInto(&out.APISchema)
	in.BackendRef.DeepCopyInto(&out.BackendRef)
	if in.BackendSecurityPolicyRef != nil {
		in, out := &in.BackendSecurityPolicyRef, &out.BackendSecurityPolicyRef
		*out = new(apisv1.LocalObjectReference)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIBackendSpec.
func (in *AIBackendSpec) DeepCopy() *AIBackendSpec {
	if in == nil {
		return nil
	}
	out := new(AIBackendSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIBackendStatus) DeepCopyInto(out *AIBackendStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIBackendStatus.
func (in *AIBackendStatus) DeepCopy() *AIBackendStatus {
	if in == nil {
		return nil
	}
	out := new(AIBackendStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIRoute) DeepCopyInto(out *AIRoute) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIRoute.
func (in *AIRoute) DeepCopy() *AIRoute {
	if in == nil {
		return nil
	}
	out := new(AIRoute)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *AIRoute) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIRouteList) DeepCopyInto(out *AIRouteList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]AIRoute, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIRouteList.
func (in *AIRouteList) DeepCopy() *AIRouteList {
	if in == nil {
		return nil
	}
	out := new(AIRouteList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *AIRouteList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIRouteMatch) DeepCopyInto(out *AIRouteMatch) {
	*out = *in
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(apisv1.HTTPPathMatch)
		(*in).DeepCopyInto(*out)
	}
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]apisv1.HTTPHeaderMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.QueryParams != nil {
		in, out := &in.QueryParams, &out.QueryParams
		*out = make([]apisv1.HTTPQueryParamMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Method != nil {
		in, out := &in.Method, &out.Method
		*out = new(apisv1.HTTPMethod)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIRouteMatch.
func (in *AIRouteMatch) DeepCopy() *AIRouteMatch {
	if in == nil {
		return nil
	}
	out := new(AIRouteMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIRouteRule) DeepCopyInto(out *AIRouteRule) {
	*out = *in
	if in.BackendRefs != nil {
		in, out := &in.BackendRefs, &out.BackendRefs
		*out = make([]AIBackendRef, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Matches != nil {
		in, out := &in.Matches, &out.Matches
		*out = make([]AIRouteMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIRouteRule.
func (in *AIRouteRule) DeepCopy() *AIRouteRule {
	if in == nil {
		return nil
	}
	out := new(AIRouteRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIRouteSpec) DeepCopyInto(out *AIRouteSpec) {
	*out = *in
	if in.TargetRefs != nil {
		in, out := &in.TargetRefs, &out.TargetRefs
		*out = make([]apisv1alpha2.LocalPolicyTargetReferenceWithSectionName, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	in.APISchema.DeepCopyInto(&out.APISchema)
	if in.Rules != nil {
		in, out := &in.Rules, &out.Rules
		*out = make([]AIRouteRule, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.FilterConfig != nil {
		in, out := &in.FilterConfig, &out.FilterConfig
		*out = new(FilterConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIRouteSpec.
func (in *AIRouteSpec) DeepCopy() *AIRouteSpec {
	if in == nil {
		return nil
	}
	out := new(AIRouteSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIRouteStatus) DeepCopyInto(out *AIRouteStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIRouteStatus.
func (in *AIRouteStatus) DeepCopy() *AIRouteStatus {
	if in == nil {
		return nil
	}
	out := new(AIRouteStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AWSOIDCExchangeToken) DeepCopyInto(out *AWSOIDCExchangeToken) {
	*out = *in
	in.BackendSecurityPolicyOIDC.DeepCopyInto(&out.BackendSecurityPolicyOIDC)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AWSOIDCExchangeToken.
func (in *AWSOIDCExchangeToken) DeepCopy() *AWSOIDCExchangeToken {
	if in == nil {
		return nil
	}
	out := new(AWSOIDCExchangeToken)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AzureOIDCExchangeToken) DeepCopyInto(out *AzureOIDCExchangeToken) {
	*out = *in
	in.BackendSecurityPolicyOIDC.DeepCopyInto(&out.BackendSecurityPolicyOIDC)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AzureOIDCExchangeToken.
func (in *AzureOIDCExchangeToken) DeepCopy() *AzureOIDCExchangeToken {
	if in == nil {
		return nil
	}
	out := new(AzureOIDCExchangeToken)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSecurityPolicy) DeepCopyInto(out *BackendSecurityPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSecurityPolicy.
func (in *BackendSecurityPolicy) DeepCopy() *BackendSecurityPolicy {
	if in == nil {
		return nil
	}
	out := new(BackendSecurityPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BackendSecurityPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSecurityPolicyAPIKey) DeepCopyInto(out *BackendSecurityPolicyAPIKey) {
	*out = *in
	if in.SecretRef != nil {
		in, out := &in.SecretRef, &out.SecretRef
		*out = new(apisv1.SecretObjectReference)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSecurityPolicyAPIKey.
func (in *BackendSecurityPolicyAPIKey) DeepCopy() *BackendSecurityPolicyAPIKey {
	if in == nil {
		return nil
	}
	out := new(BackendSecurityPolicyAPIKey)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSecurityPolicyAWSCredentials) DeepCopyInto(out *BackendSecurityPolicyAWSCredentials) {
	*out = *in
	if in.SecretRef != nil {
		in, out := &in.SecretRef, &out.SecretRef
		*out = new(apisv1.SecretObjectReference)
		(*in).DeepCopyInto(*out)
	}
	if in.OIDCExchangeToken != nil {
		in, out := &in.OIDCExchangeToken, &out.OIDCExchangeToken
		*out = new(AWSOIDCExchangeToken)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSecurityPolicyAWSCredentials.
func (in *BackendSecurityPolicyAWSCredentials) DeepCopy() *BackendSecurityPolicyAWSCredentials {
	if in == nil {
		return nil
	}
	out := new(BackendSecurityPolicyAWSCredentials)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSecurityPolicyAzureCredentials) DeepCopyInto(out *BackendSecurityPolicyAzureCredentials) {
	*out = *in
	if in.ClientSecretRef != nil {
		in, out := &in.ClientSecretRef, &out.ClientSecretRef
		*out = new(apisv1.SecretObjectReference)
		(*in).DeepCopyInto(*out)
	}
	if in.OIDCExchangeToken != nil {
		in, out := &in.OIDCExchangeToken, &out.OIDCExchangeToken
		*out = new(AzureOIDCExchangeToken)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSecurityPolicyAzureCredentials.
func (in *BackendSecurityPolicyAzureCredentials) DeepCopy() *BackendSecurityPolicyAzureCredentials {
	if in == nil {
		return nil
	}
	out := new(BackendSecurityPolicyAzureCredentials)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSecurityPolicyGCPCredentials) DeepCopyInto(out *BackendSecurityPolicyGCPCredentials) {
	*out = *in
	in.WorkLoadIdentityFederationConfig.DeepCopyInto(&out.WorkLoadIdentityFederationConfig)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSecurityPolicyGCPCredentials.
func (in *BackendSecurityPolicyGCPCredentials) DeepCopy() *BackendSecurityPolicyGCPCredentials {
	if in == nil {
		return nil
	}
	out := new(BackendSecurityPolicyGCPCredentials)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSecurityPolicyList) DeepCopyInto(out *BackendSecurityPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]BackendSecurityPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSecurityPolicyList.
func (in *BackendSecurityPolicyList) DeepCopy() *BackendSecurityPolicyList {
	if in == nil {
		return nil
	}
	out := new(BackendSecurityPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BackendSecurityPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSecurityPolicyOIDC) DeepCopyInto(out *BackendSecurityPolicyOIDC) {
	*out = *in
	in.OIDC.DeepCopyInto(&out.OIDC)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSecurityPolicyOIDC.
func (in *BackendSecurityPolicyOIDC) DeepCopy() *BackendSecurityPolicyOIDC {
	if in == nil {
		return nil
	}
	out := new(BackendSecurityPolicyOIDC)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSecurityPolicySpec) DeepCopyInto(out *BackendSecurityPolicySpec) {
	*out = *in
	if in.APIKey != nil {
		in, out := &in.APIKey, &out.APIKey
		*out = new(BackendSecurityPolicyAPIKey)
		(*in).DeepCopyInto(*out)
	}
	if in.AWSCredentials != nil {
		in, out := &in.AWSCredentials, &out.AWSCredentials
		*out = new(BackendSecurityPolicyAWSCredentials)
		(*in).DeepCopyInto(*out)
	}
	if in.AzureCredentials != nil {
		in, out := &in.AzureCredentials, &out.AzureCredentials
		*out = new(BackendSecurityPolicyAzureCredentials)
		(*in).DeepCopyInto(*out)
	}
	if in.GCPCredentials != nil {
		in, out := &in.GCPCredentials, &out.GCPCredentials
		*out = new(BackendSecurityPolicyGCPCredentials)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSecurityPolicySpec.
func (in *BackendSecurityPolicySpec) DeepCopy() *BackendSecurityPolicySpec {
	if in == nil {
		return nil
	}
	out := new(BackendSecurityPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSecurityPolicyStatus) DeepCopyInto(out *BackendSecurityPolicyStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSecurityPolicyStatus.
func (in *BackendSecurityPolicyStatus) DeepCopy() *BackendSecurityPolicyStatus {
	if in == nil {
		return nil
	}
	out := new(BackendSecurityPolicyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtProcConfig) DeepCopyInto(out *ExtProcConfig) {
	*out = *in
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = new(v1.ResourceRequirements)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtProcConfig.
func (in *ExtProcConfig) DeepCopy() *ExtProcConfig {
	if in == nil {
		return nil
	}
	out := new(ExtProcConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FilterConfig) DeepCopyInto(out *FilterConfig) {
	*out = *in
	if in.LLMRequestCosts != nil {
		in, out := &in.LLMRequestCosts, &out.LLMRequestCosts
		*out = make([]LLMRequestCost, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ExtProcConfig != nil {
		in, out := &in.ExtProcConfig, &out.ExtProcConfig
		*out = new(ExtProcConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FilterConfig.
func (in *FilterConfig) DeepCopy() *FilterConfig {
	if in == nil {
		return nil
	}
	out := new(FilterConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GCPServiceAccountImpersonationConfig) DeepCopyInto(out *GCPServiceAccountImpersonationConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GCPServiceAccountImpersonationConfig.
func (in *GCPServiceAccountImpersonationConfig) DeepCopy() *GCPServiceAccountImpersonationConfig {
	if in == nil {
		return nil
	}
	out := new(GCPServiceAccountImpersonationConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GCPWorkLoadIdentityFederationConfig) DeepCopyInto(out *GCPWorkLoadIdentityFederationConfig) {
	*out = *in
	in.WorkloadIdentityProvider.DeepCopyInto(&out.WorkloadIdentityProvider)
	if in.ServiceAccountImpersonation != nil {
		in, out := &in.ServiceAccountImpersonation, &out.ServiceAccountImpersonation
		*out = new(GCPServiceAccountImpersonationConfig)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GCPWorkLoadIdentityFederationConfig.
func (in *GCPWorkLoadIdentityFederationConfig) DeepCopy() *GCPWorkLoadIdentityFederationConfig {
	if in == nil {
		return nil
	}
	out := new(GCPWorkLoadIdentityFederationConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GCPWorkloadIdentityProvider) DeepCopyInto(out *GCPWorkloadIdentityProvider) {
	*out = *in
	in.OIDCProvider.DeepCopyInto(&out.OIDCProvider)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GCPWorkloadIdentityProvider.
func (in *GCPWorkloadIdentityProvider) DeepCopy() *GCPWorkloadIdentityProvider {
	if in == nil {
		return nil
	}
	out := new(GCPWorkloadIdentityProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LLMRequestCost) DeepCopyInto(out *LLMRequestCost) {
	*out = *in
	if in.CEL != nil {
		in, out := &in.CEL, &out.CEL
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LLMRequestCost.
func (in *LLMRequestCost) DeepCopy() *LLMRequestCost {
	if in == nil {
		return nil
	}
	out := new(LLMRequestCost)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VersionedAPISchema) DeepCopyInto(out *VersionedAPISchema) {
	*out = *in
	if in.Version != nil {
		in, out := &in.Version, &out.Version
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VersionedAPISchema.
func (in *VersionedAPISchema) DeepCopy() *VersionedAPISchema {
	if in == nil {
		return nil
	}
	out := new(VersionedAPISchema)
	in.DeepCopyInto(out)
	return out
}
