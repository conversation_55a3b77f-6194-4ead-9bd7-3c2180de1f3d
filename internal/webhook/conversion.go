// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package webhook

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/go-logr/logr"
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/conversion"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"

	aigv1a1 "github.com/envoyproxy/ai-gateway/api/v1alpha1"
	aigv1a2 "github.com/envoyproxy/ai-gateway/api/v1alpha2"
)

// ConversionWebhook handles CRD conversion between v1alpha1 and v1alpha2 API versions.
type ConversionWebhook struct {
	scheme *runtime.Scheme
	logger logr.Logger
}

// NewConversionWebhook creates a new conversion webhook handler.
func NewConversionWebhook(scheme *runtime.Scheme, logger logr.Logger) *ConversionWebhook {
	return &ConversionWebhook{
		scheme: scheme,
		logger: logger,
	}
}

// Handle processes conversion webhook requests.
func (w *ConversionWebhook) Handle(ctx context.Context, req admission.Request) admission.Response {
	w.logger.Info("Processing conversion request", "uid", req.UID)

	var conversionRequest apiextensionsv1.ConversionRequest
	if err := json.Unmarshal(req.Object.Raw, &conversionRequest); err != nil {
		w.logger.Error(err, "Failed to unmarshal conversion request")
		return admission.Errored(http.StatusBadRequest, fmt.Errorf("failed to unmarshal conversion request: %w", err))
	}

	conversionResponse := w.convertObjects(conversionRequest)
	conversionResponse.UID = conversionRequest.UID

	responseBytes, err := json.Marshal(conversionResponse)
	if err != nil {
		w.logger.Error(err, "Failed to marshal conversion response")
		return admission.Errored(http.StatusInternalServerError, fmt.Errorf("failed to marshal conversion response: %w", err))
	}

	return admission.Response{
		AdmissionResponse: admission.AdmissionResponse{
			UID:    req.UID,
			Result: &metav1.Status{Code: http.StatusOK},
		},
		Patches: nil,
		Raw:     responseBytes,
	}
}

// convertObjects performs the actual conversion between API versions.
func (w *ConversionWebhook) convertObjects(req apiextensionsv1.ConversionRequest) *apiextensionsv1.ConversionResponse {
	response := &apiextensionsv1.ConversionResponse{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "apiextensions.k8s.io/v1",
			Kind:       "ConversionResponse",
		},
		Result: metav1.Status{
			Status: metav1.StatusSuccess,
		},
	}

	for _, obj := range req.Objects {
		convertedObj, err := w.convertObject(obj, req.DesiredAPIVersion)
		if err != nil {
			w.logger.Error(err, "Failed to convert object", "desiredVersion", req.DesiredAPIVersion)
			response.Result = metav1.Status{
				Status:  metav1.StatusFailure,
				Message: err.Error(),
			}
			return response
		}
		response.ConvertedObjects = append(response.ConvertedObjects, convertedObj)
	}

	return response
}

// convertObject converts a single object between API versions.
func (w *ConversionWebhook) convertObject(obj runtime.RawExtension, desiredAPIVersion string) (runtime.RawExtension, error) {
	// Parse the object to determine its current version and kind
	var objMeta metav1.Object
	if err := json.Unmarshal(obj.Raw, &objMeta); err != nil {
		return runtime.RawExtension{}, fmt.Errorf("failed to unmarshal object metadata: %w", err)
	}

	var typeMeta metav1.TypeMeta
	if err := json.Unmarshal(obj.Raw, &typeMeta); err != nil {
		return runtime.RawExtension{}, fmt.Errorf("failed to unmarshal object type metadata: %w", err)
	}

	w.logger.Info("Converting object", 
		"kind", typeMeta.Kind, 
		"currentVersion", typeMeta.APIVersion, 
		"desiredVersion", desiredAPIVersion)

	// If already at desired version, return as-is
	if typeMeta.APIVersion == desiredAPIVersion {
		return obj, nil
	}

	// Perform conversion based on kind and versions
	switch typeMeta.Kind {
	case "AIGatewayRoute", "AIRoute":
		return w.convertAIRoute(obj, typeMeta.APIVersion, desiredAPIVersion)
	case "AIServiceBackend", "AIBackend":
		return w.convertAIBackend(obj, typeMeta.APIVersion, desiredAPIVersion)
	case "BackendSecurityPolicy":
		return w.convertBackendSecurityPolicy(obj, typeMeta.APIVersion, desiredAPIVersion)
	default:
		return runtime.RawExtension{}, fmt.Errorf("unsupported kind for conversion: %s", typeMeta.Kind)
	}
}

// convertAIRoute converts between AIGatewayRoute (v1alpha1) and AIRoute (v1alpha2).
func (w *ConversionWebhook) convertAIRoute(obj runtime.RawExtension, currentVersion, desiredVersion string) (runtime.RawExtension, error) {
	switch {
	case currentVersion == "aigateway.envoyproxy.io/v1alpha1" && desiredVersion == "aigateway.envoyproxy.io/v1alpha2":
		// Convert from v1alpha1 to v1alpha2
		var v1alpha1Obj aigv1a1.AIGatewayRoute
		if err := json.Unmarshal(obj.Raw, &v1alpha1Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to unmarshal v1alpha1 AIGatewayRoute: %w", err)
		}

		var v1alpha2Obj aigv1a2.AIRoute
		if err := v1alpha2Obj.ConvertFrom(&v1alpha1Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to convert from v1alpha1 to v1alpha2: %w", err)
		}

		// Set the correct API version and kind
		v1alpha2Obj.APIVersion = desiredVersion
		v1alpha2Obj.Kind = "AIRoute"

		convertedBytes, err := json.Marshal(v1alpha2Obj)
		if err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to marshal v1alpha2 AIRoute: %w", err)
		}

		return runtime.RawExtension{Raw: convertedBytes}, nil

	case currentVersion == "aigateway.envoyproxy.io/v1alpha2" && desiredVersion == "aigateway.envoyproxy.io/v1alpha1":
		// Convert from v1alpha2 to v1alpha1
		var v1alpha2Obj aigv1a2.AIRoute
		if err := json.Unmarshal(obj.Raw, &v1alpha2Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to unmarshal v1alpha2 AIRoute: %w", err)
		}

		var v1alpha1Obj aigv1a1.AIGatewayRoute
		if err := v1alpha2Obj.ConvertTo(&v1alpha1Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to convert from v1alpha2 to v1alpha1: %w", err)
		}

		// Set the correct API version and kind
		v1alpha1Obj.APIVersion = desiredVersion
		v1alpha1Obj.Kind = "AIGatewayRoute"

		convertedBytes, err := json.Marshal(v1alpha1Obj)
		if err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to marshal v1alpha1 AIGatewayRoute: %w", err)
		}

		return runtime.RawExtension{Raw: convertedBytes}, nil

	default:
		return runtime.RawExtension{}, fmt.Errorf("unsupported conversion from %s to %s for AIRoute", currentVersion, desiredVersion)
	}
}

// convertAIBackend converts between AIServiceBackend (v1alpha1) and AIBackend (v1alpha2).
func (w *ConversionWebhook) convertAIBackend(obj runtime.RawExtension, currentVersion, desiredVersion string) (runtime.RawExtension, error) {
	switch {
	case currentVersion == "aigateway.envoyproxy.io/v1alpha1" && desiredVersion == "aigateway.envoyproxy.io/v1alpha2":
		// Convert from v1alpha1 to v1alpha2
		var v1alpha1Obj aigv1a1.AIServiceBackend
		if err := json.Unmarshal(obj.Raw, &v1alpha1Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to unmarshal v1alpha1 AIServiceBackend: %w", err)
		}

		var v1alpha2Obj aigv1a2.AIBackend
		if err := v1alpha2Obj.ConvertFrom(&v1alpha1Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to convert from v1alpha1 to v1alpha2: %w", err)
		}

		// Set the correct API version and kind
		v1alpha2Obj.APIVersion = desiredVersion
		v1alpha2Obj.Kind = "AIBackend"

		convertedBytes, err := json.Marshal(v1alpha2Obj)
		if err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to marshal v1alpha2 AIBackend: %w", err)
		}

		return runtime.RawExtension{Raw: convertedBytes}, nil

	case currentVersion == "aigateway.envoyproxy.io/v1alpha2" && desiredVersion == "aigateway.envoyproxy.io/v1alpha1":
		// Convert from v1alpha2 to v1alpha1
		var v1alpha2Obj aigv1a2.AIBackend
		if err := json.Unmarshal(obj.Raw, &v1alpha2Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to unmarshal v1alpha2 AIBackend: %w", err)
		}

		var v1alpha1Obj aigv1a1.AIServiceBackend
		if err := v1alpha2Obj.ConvertTo(&v1alpha1Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to convert from v1alpha2 to v1alpha1: %w", err)
		}

		// Set the correct API version and kind
		v1alpha1Obj.APIVersion = desiredVersion
		v1alpha1Obj.Kind = "AIServiceBackend"

		convertedBytes, err := json.Marshal(v1alpha1Obj)
		if err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to marshal v1alpha1 AIServiceBackend: %w", err)
		}

		return runtime.RawExtension{Raw: convertedBytes}, nil

	default:
		return runtime.RawExtension{}, fmt.Errorf("unsupported conversion from %s to %s for AIBackend", currentVersion, desiredVersion)
	}
}

// convertBackendSecurityPolicy converts BackendSecurityPolicy between versions.
func (w *ConversionWebhook) convertBackendSecurityPolicy(obj runtime.RawExtension, currentVersion, desiredVersion string) (runtime.RawExtension, error) {
	switch {
	case currentVersion == "aigateway.envoyproxy.io/v1alpha1" && desiredVersion == "aigateway.envoyproxy.io/v1alpha2":
		// Convert from v1alpha1 to v1alpha2
		var v1alpha1Obj aigv1a1.BackendSecurityPolicy
		if err := json.Unmarshal(obj.Raw, &v1alpha1Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to unmarshal v1alpha1 BackendSecurityPolicy: %w", err)
		}

		var v1alpha2Obj aigv1a2.BackendSecurityPolicy
		if err := v1alpha2Obj.ConvertFrom(&v1alpha1Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to convert from v1alpha1 to v1alpha2: %w", err)
		}

		// Set the correct API version
		v1alpha2Obj.APIVersion = desiredVersion
		v1alpha2Obj.Kind = "BackendSecurityPolicy"

		convertedBytes, err := json.Marshal(v1alpha2Obj)
		if err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to marshal v1alpha2 BackendSecurityPolicy: %w", err)
		}

		return runtime.RawExtension{Raw: convertedBytes}, nil

	case currentVersion == "aigateway.envoyproxy.io/v1alpha2" && desiredVersion == "aigateway.envoyproxy.io/v1alpha1":
		// Convert from v1alpha2 to v1alpha1
		var v1alpha2Obj aigv1a2.BackendSecurityPolicy
		if err := json.Unmarshal(obj.Raw, &v1alpha2Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to unmarshal v1alpha2 BackendSecurityPolicy: %w", err)
		}

		var v1alpha1Obj aigv1a1.BackendSecurityPolicy
		if err := v1alpha2Obj.ConvertTo(&v1alpha1Obj); err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to convert from v1alpha2 to v1alpha1: %w", err)
		}

		// Set the correct API version
		v1alpha1Obj.APIVersion = desiredVersion
		v1alpha1Obj.Kind = "BackendSecurityPolicy"

		convertedBytes, err := json.Marshal(v1alpha1Obj)
		if err != nil {
			return runtime.RawExtension{}, fmt.Errorf("failed to marshal v1alpha1 BackendSecurityPolicy: %w", err)
		}

		return runtime.RawExtension{Raw: convertedBytes}, nil

	default:
		return runtime.RawExtension{}, fmt.Errorf("unsupported conversion from %s to %s for BackendSecurityPolicy", currentVersion, desiredVersion)
	}
}
