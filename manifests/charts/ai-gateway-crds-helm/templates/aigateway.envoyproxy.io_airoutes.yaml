---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.3
  name: airoutes.aigateway.envoyproxy.io
spec:
  group: aigateway.envoyproxy.io
  names:
    kind: AIRoute
    listKind: AIRouteList
    plural: airoutes
    singular: airoute
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.conditions[-1:].type
      name: Status
      type: string
    name: v1alpha2
    schema:
      openAPIV3Schema:
        description: |-
          AIRoute combines multiple AIBackends and attaching them to Gateway(s) resources.

          This serves as a way to define a "unified" AI API for a Gateway which allows downstream
          clients to use a single schema API to interact with multiple AI backends.

          The schema field is used to determine the structure of the requests that the Gateway will
          receive. And then the Gateway will route the traffic to the appropriate AIBackend based
          on the output schema of the AIBackend while doing the other necessary jobs like
          upstream authentication, rate limit, etc.

          Envoy AI Gateway will generate the following k8s resources corresponding to the AIRoute:

            - HTTPRoute of the Gateway API as a top-level resource to bind all backends.
              The name of the HTTPRoute is the same as the AIRoute.
            - EnvoyExtensionPolicy of the Envoy Gateway API to attach the AI Gateway filter into the target Gateways.
              This will be created per Gateway, and its name is `ai-eg-eep-${gateway-name}`.
            - HTTPRouteFilter of the Envoy Gateway API per namespace for automatic hostname rewrite.
              The name of the HTTPRouteFilter is `ai-eg-host-rewrite`.

          All of these resources are created in the same namespace as the AIRoute. Note that this is the implementation
          detail subject to change. If you want to customize the default behavior of the Envoy AI Gateway, you can use these
          resources as a reference and create your own resources. Alternatively, you can use EnvoyPatchPolicy API of the Envoy
          Gateway to patch the generated resources. For example, you can configure the retry fallback behavior by attaching
          BackendTrafficPolicy API of Envoy Gateway to the generated HTTPRoute.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the details of the AIRoute.
            properties:
              filterConfig:
                description: |-
                  FilterConfig is the configuration for the AI Gateway filter inserted in the generated HTTPRoute.

                  An AI Gateway filter is responsible for the transformation of the request and response
                  as well as the routing behavior based on the model name extracted from the request content, etc.
                properties:
                  extProcConfig:
                    description: ExtProcConfig is the configuration for the external
                      processor.
                    properties:
                      resources:
                        description: |-
                          Resources is the resource requirements for the external processor container.
                          This allows users to configure the resource requirements of the external processor container.
                        properties:
                          claims:
                            description: |-
                              Claims lists the names of resources, defined in spec.resourceClaims,
                              that are used by this container.

                              This is an alpha field and requires enabling the
                              DynamicResourceAllocation feature gate.

                              This field is immutable. It can only be set for containers.
                            items:
                              description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                              properties:
                                name:
                                  description: |-
                                    Name must match the name of one entry in pod.spec.resourceClaims of
                                    the Pod where this field is used. It makes that resource available
                                    inside a container.
                                  type: string
                                request:
                                  description: |-
                                    Request is the name chosen for a request in the referenced claim.
                                    If empty, everything from the claim is made available, otherwise
                                    only the result of this request.
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                          limits:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            description: |-
                              Limits describes the maximum amount of compute resources allowed.
                              More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                            type: object
                          requests:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            description: |-
                              Requests describes the minimum amount of compute resources required.
                              If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                              otherwise to an implementation-defined value. Requests cannot exceed Limits.
                              More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                            type: object
                        type: object
                    type: object
                  llmRequestCosts:
                    description: LLMRequestCosts is the list of LLMRequestCost that
                      this AIRoute will use to calculate the cost of the request.
                    items:
                      description: LLMRequestCost configures each request cost.
                      properties:
                        cel:
                          description: "CEL is the CEL expression to calculate the
                            cost of the request.\nThe CEL expression must return a
                            signed or unsigned integer. If the\nreturn value is negative,
                            it will be error.\n\nThe expression can use the following
                            variables:\n\n\t* model: the model name extracted from
                            the request content. Type: string.\n\t* backend: the backend
                            name in the form of \"name.namespace\". Type: string.\n\t*
                            input_tokens: the number of input tokens. Type: unsigned
                            integer.\n\t* output_tokens: the number of output tokens.
                            Type: unsigned integer.\n\t* total_tokens: the total number
                            of tokens. Type: unsigned integer.\n\nFor example, the
                            following expressions are valid:\n\n\t* \"model == 'llama'
                            ?  input_tokens + output_token * 0.5 : total_tokens\"\n\t*
                            \"backend == 'foo.default' ?  input_tokens + output_tokens
                            : total_tokens\"\n\t* \"input_tokens + output_tokens +
                            total_tokens\"\n\t* \"input_tokens * output_tokens\""
                          type: string
                        metadataKey:
                          description: MetadataKey is the key of the metadata to store
                            this cost of the request.
                          type: string
                        type:
                          description: |-
                            Type specifies the type of the request cost. The default is "OutputToken",
                            and it uses "output token" as the cost. The other types are "InputToken", "TotalToken",
                            and "CEL".
                          enum:
                          - OutputToken
                          - InputToken
                          - TotalToken
                          - CEL
                          type: string
                      required:
                      - metadataKey
                      - type
                      type: object
                    maxItems: 128
                    type: array
                type: object
              rules:
                description: |-
                  Rules is the list of AIRouteRule that this AIRoute will match the traffic to.
                  Each rule is a subset of the HTTPRoute in the Gateway API (https://gateway-api.sigs.k8s.io/api-types/httproute/).

                  AI Gateway controller will generate a HTTPRoute based on the configuration given here with the additional
                  modifications to achieve the necessary jobs, notably inserting the AI Gateway filter responsible for
                  the transformation of the request and response, etc.

                  In the matching conditions in the AIRouteRule, `x-ai-eg-model` header is available
                  if we want to describe the routing behavior based on the model name. The model name is extracted
                  from the request content before the routing decision.

                  How multiple rules are matched is the same as the Gateway API. See for the details:
                  https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.HTTPRoute
                items:
                  description: AIRouteRule is a rule that defines the routing behavior
                    of the AIRoute.
                  properties:
                    backendRefs:
                      description: |-
                        BackendRefs is the list of AIBackend that this rule will route the traffic to.
                        Each backend can have a weight that determines the traffic distribution.

                        The namespace of each backend is "local", i.e. the same namespace as the AIRoute.

                        By configuring multiple backends, you can achieve the fallback behavior in the case of
                        the primary backend is not available combined with the BackendTrafficPolicy of Envoy Gateway.
                        Please refer to https://gateway.envoyproxy.io/docs/tasks/traffic/failover/ as well as
                        https://gateway.envoyproxy.io/docs/tasks/traffic/retry/.
                      items:
                        description: AIBackendRef is a reference to an AIBackend resource.
                        properties:
                          modelNameOverride:
                            description: Name of the model in the backend. If provided
                              this will override the name provided in the request.
                            type: string
                          name:
                            description: Name is the name of the AIBackend resource.
                            maxLength: 253
                            minLength: 1
                            type: string
                          priority:
                            default: 0
                            description: |-
                              Priority is the priority of the AIBackend. This sets the priority on the underlying endpoints.
                              See: https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/load_balancing/priority
                              Note: This will override the `faillback` property of the underlying Envoy Gateway Backend

                              Default is 0.
                            format: int32
                            minimum: 0
                            type: integer
                          weight:
                            default: 1
                            description: |-
                              Weight is the weight of the AIBackend. This is exactly the same as the weight in
                              the BackendRef in the Gateway API. See for the details:
                              https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.BackendRef

                              Default is 1.
                            format: int32
                            minimum: 0
                            type: integer
                        required:
                        - name
                        type: object
                      maxItems: 128
                      type: array
                    matches:
                      description: |-
                        Matches define conditions used for matching the rule against incoming HTTP requests.
                        Each match is independent, i.e. this rule will be matched if **any** one of the
                        matches is satisfied.

                        For example, take the following matches configuration:

                        ```
                        matches:
                        - path:
                            type: PathPrefix
                            value: "/foo"
                          headers:
                          - name: "version"
                            value: "v2"
                        - path:
                            type: PathPrefix
                            value: "/v2/foo"
                        ```

                        For a request to match against this rule, a request must satisfy
                        EITHER of the two conditions:

                        1. path prefixed with `/foo` AND contains the header `version: v2`
                        2. path prefixed with `/v2/foo`

                        See the documentation for HTTPRoute rule matching for more details.
                      items:
                        description: "AIRouteMatch defines the predicate used to match
                          requests to a given\naction. Multiple match types are ANDed
                          together, i.e. the match will\nevaluate to true only if
                          all conditions are satisfied.\n\nFor example, the match
                          below will match a HTTP request only if its path\nstarts
                          with `/foo` AND it contains the `version: v1` header:\n\n```\nmatch:\n\n\tpath:\n\t
                          \ type: \"PathPrefix\"\n\t  value: \"/foo\"\n\theaders:\n\t-
                          name: \"version\"\n\t  value \"v1\"\n\n```"
                        properties:
                          headers:
                            description: |-
                              Headers specifies HTTP request header matchers. Multiple match values are
                              ANDed together, meaning, a request must match all the specified headers
                              to select the route.
                            items:
                              description: |-
                                HTTPHeaderMatch describes how to select a HTTP route by matching HTTP request
                                headers.
                              properties:
                                name:
                                  description: |-
                                    Name is the name of the HTTP Header to be matched. Name matching MUST be
                                    case-insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).

                                    If multiple entries specify equivalent header names, only the first
                                    entry with an equivalent name MUST be considered for a match. Subsequent
                                    entries with an equivalent header name MUST be ignored. Due to the
                                    case-insensitivity of header names, "foo" and "Foo" are considered
                                    equivalent.

                                    When a header is repeated in an HTTP request, it is
                                    implementation-specific behavior as to how this is represented.
                                    Generally, proxies should follow the guidance from the RFC:
                                    https://www.rfc-editor.org/rfc/rfc7230.html#section-3.2.2 regarding
                                    processing a repeated header, with special handling for "Set-Cookie".
                                  maxLength: 256
                                  minLength: 1
                                  pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                  type: string
                                type:
                                  default: Exact
                                  description: |-
                                    Type specifies how to match against the value of the header.

                                    Support: Core (Exact)

                                    Support: Implementation-specific (RegularExpression)

                                    Since RegularExpression HeaderMatchType has implementation-specific
                                    conformance, implementations can support POSIX, PCRE or any other dialects
                                    of regular expressions. Please read the implementation's documentation to
                                    determine the supported dialect.
                                  enum:
                                  - Exact
                                  - RegularExpression
                                  type: string
                                value:
                                  description: Value is the value of HTTP Header to
                                    be matched.
                                  maxLength: 4096
                                  minLength: 1
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            maxItems: 16
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                          method:
                            description: |-
                              Method specifies HTTP method matcher.
                              When specified, this route will be matched only if the request has the
                              specified method.

                              Support: Extended
                            enum:
                            - GET
                            - HEAD
                            - POST
                            - PUT
                            - DELETE
                            - CONNECT
                            - OPTIONS
                            - TRACE
                            - PATCH
                            type: string
                          path:
                            default:
                              type: PathPrefix
                              value: /
                            description: |-
                              Path specifies a HTTP request path matcher. If this field is not
                              specified, a default prefix match on the "/" path is provided.
                            properties:
                              type:
                                default: PathPrefix
                                description: |-
                                  Type specifies how to match against the path Value.

                                  Support: Core (Exact, PathPrefix)

                                  Support: Implementation-specific (RegularExpression)
                                enum:
                                - Exact
                                - PathPrefix
                                - RegularExpression
                                type: string
                              value:
                                default: /
                                description: Value of the HTTP path to match against.
                                maxLength: 1024
                                type: string
                            type: object
                            x-kubernetes-validations:
                            - message: value must be an absolute path and start with
                                '/' when type one of ['Exact', 'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? self.value.startsWith(''/'')
                                : true'
                            - message: must not contain '//' when type one of ['Exact',
                                'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? !self.value.contains(''//'')
                                : true'
                            - message: must not contain '/./' when type one of ['Exact',
                                'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? !self.value.contains(''/./'')
                                : true'
                            - message: must not contain '/../' when type one of ['Exact',
                                'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? !self.value.contains(''/../'')
                                : true'
                            - message: must not contain '%2f' when type one of ['Exact',
                                'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? !self.value.contains(''%2f'')
                                : true'
                            - message: must not contain '%2F' when type one of ['Exact',
                                'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? !self.value.contains(''%2F'')
                                : true'
                            - message: must not contain '#' when type one of ['Exact',
                                'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? !self.value.contains(''#'')
                                : true'
                            - message: must not end with '/..' when type one of ['Exact',
                                'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? !self.value.endsWith(''/..'')
                                : true'
                            - message: must not end with '/.' when type one of ['Exact',
                                'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? !self.value.endsWith(''/.'')
                                : true'
                            - message: type must be one of ['Exact', 'PathPrefix',
                                'RegularExpression']
                              rule: self.type in ['Exact','PathPrefix'] || self.type
                                == 'RegularExpression'
                            - message: must only contain valid characters (matching
                                ^(?:[-A-Za-z0-9/._~!$&'()*+,;=:@]|[%][0-9a-fA-F]{2})+$)
                                for types ['Exact', 'PathPrefix']
                              rule: '(self.type in [''Exact'',''PathPrefix'']) ? self.value.matches(r"""^(?:[-A-Za-z0-9/._~!$&''()*+,;=:@]|[%][0-9a-fA-F]{2})+$""")
                                : true'
                          queryParams:
                            description: |-
                              QueryParams specifies HTTP query parameter matchers. Multiple match
                              values are ANDed together, meaning, a request must match all the
                              specified query parameters to select the route.

                              Support: Extended
                            items:
                              description: |-
                                HTTPQueryParamMatch describes how to select a HTTP route by matching HTTP
                                query parameters.
                              properties:
                                name:
                                  description: |-
                                    Name is the name of the HTTP query param to be matched. This must be an
                                    exact string match. (See
                                    https://tools.ietf.org/html/rfc7230#section-2.7.3).

                                    If multiple entries specify equivalent query param names, only the first
                                    entry with an equivalent name MUST be considered for a match. Subsequent
                                    entries with an equivalent query param name MUST be ignored.

                                    If a query param is repeated in an HTTP request, the behavior is
                                    purposely left undefined, since different data planes have different
                                    capabilities. However, it is *recommended* that implementations should
                                    match against the first value of the param if the data plane supports it,
                                    as this behavior is expected in other load balancing contexts outside of
                                    the Gateway API.

                                    Users SHOULD NOT route traffic based on repeated query params to guard
                                    themselves against potential differences in the implementations.
                                  maxLength: 256
                                  minLength: 1
                                  pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                  type: string
                                type:
                                  default: Exact
                                  description: |-
                                    Type specifies how to match against the value of the query parameter.

                                    Support: Extended (Exact)

                                    Support: Implementation-specific (RegularExpression)

                                    Since RegularExpression QueryParamMatchType has Implementation-specific
                                    conformance, implementations can support POSIX, PCRE or any other
                                    dialects of regular expressions. Please read the implementation's
                                    documentation to determine the supported dialect.
                                  enum:
                                  - Exact
                                  - RegularExpression
                                  type: string
                                value:
                                  description: Value is the value of HTTP query param
                                    to be matched.
                                  maxLength: 1024
                                  minLength: 1
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            maxItems: 16
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                        type: object
                      maxItems: 8
                      type: array
                  type: object
                maxItems: 128
                type: array
              schema:
                description: |-
                  APISchema specifies the API schema of the input that the target Gateway(s) will receive.
                  Based on this schema, the ai-gateway will perform the necessary transformation to the
                  output schema specified in the selected AIBackend during the routing process.

                  Currently, the only supported schema is OpenAI as the input schema.
                properties:
                  name:
                    description: Name is the name of the API schema of the AIRoute
                      or AIBackend.
                    enum:
                    - OpenAI
                    - AWSBedrock
                    - AzureOpenAI
                    - GCPVertexAI
                    - GCPAnthropic
                    type: string
                  version:
                    description: |-
                      Version is the version of the API schema.

                      When the name is set to "OpenAI", this equals to the prefix of the OpenAI API endpoints. This defaults to "v1"
                      if not set or empty string. For example, "chat completions" API endpoint will be "/v1/chat/completions"
                      if the version is set to "v1".

                      This is especially useful when routing to the backend that has an OpenAI compatible API but has a different
                      versioning scheme. For example, Gemini OpenAI compatible API (https://ai.google.dev/gemini-api/docs/openai) uses
                      "/v1beta/openai" version prefix. Another example is that Cohere AI (https://docs.cohere.com/v2/docs/compatibility-api)
                      uses "/compatibility/v1" version prefix. On the other hand, DeepSeek (https://api-docs.deepseek.com/) doesn't
                      use version prefix, so the version can be set to an empty string.

                      When the name is set to AzureOpenAI, this version maps to "API Version" in the
                      Azure OpenAI API documentation (https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#rest-api-versioning).
                    type: string
                required:
                - name
                type: object
                x-kubernetes-validations:
                - rule: self.name == 'OpenAI'
              targetRefs:
                description: TargetRefs are the names of the Gateway resources this
                  AIRoute is being attached to.
                items:
                  description: |-
                    LocalPolicyTargetReferenceWithSectionName identifies an API object to apply a
                    direct policy to. This should be used as part of Policy resources that can
                    target single resources. For more information on how this policy attachment
                    mode works, and a sample Policy resource, refer to the policy attachment
                    documentation for Gateway API.

                    Note: This should only be used for direct policy attachment when references
                    to SectionName are actually needed. In all other cases,
                    LocalPolicyTargetReference should be used.
                  properties:
                    group:
                      description: Group is the group of the target resource.
                      maxLength: 253
                      pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                    kind:
                      description: Kind is kind of the target resource.
                      maxLength: 63
                      minLength: 1
                      pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                      type: string
                    name:
                      description: Name is the name of the target resource.
                      maxLength: 253
                      minLength: 1
                      type: string
                    sectionName:
                      description: |-
                        SectionName is the name of a section within the target resource. When
                        unspecified, this targetRef targets the entire resource. In the following
                        resources, SectionName is interpreted as the following:

                        * Gateway: Listener name
                        * HTTPRoute: HTTPRouteRule name
                        * Service: Port name

                        If a SectionName is specified, but does not exist on the targeted object,
                        the Policy must fail to attach, and the policy implementation should record
                        a `ResolvedRefs` or similar Condition in the Policy's status.
                      maxLength: 253
                      minLength: 1
                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                  required:
                  - group
                  - kind
                  - name
                  type: object
                maxItems: 128
                minItems: 1
                type: array
            required:
            - rules
            - schema
            - targetRefs
            type: object
          status:
            description: Status defines the status details of the AIRoute.
            properties:
              conditions:
                description: |-
                  Conditions is the list of conditions by the reconciliation result.
                  Currently, at most one condition is set.

                  Known .status.conditions.type are: "Accepted", "NotAccepted".
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
